<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiteOFD 开发演示导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 50px;
        }
        
        .section {
            margin-bottom: 50px;
        }
        
        .section h2 {
            color: #495057;
            margin-bottom: 25px;
            font-size: 2rem;
            text-align: center;
        }
        
        .demos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }
        
        .demo-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            transition: all 0.3s;
            border: 1px solid #e9ecef;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .demo-card.featured {
            background: linear-gradient(135deg, #e7f3ff 0%, #f0f8ff 100%);
            border-color: #4a90e2;
        }
        
        .demo-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .demo-icon {
            font-size: 2.5rem;
            margin-right: 15px;
        }
        
        .demo-title {
            color: #495057;
            font-size: 1.4rem;
            font-weight: 600;
        }
        
        .demo-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .demo-button {
            display: inline-block;
            padding: 12px 24px;
            background: #4a90e2;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s;
            text-align: center;
            width: 100%;
        }
        
        .demo-button:hover {
            background: #357abd;
            transform: translateY(-1px);
        }
        
        .demo-button.secondary {
            background: #6c757d;
        }
        
        .demo-button.secondary:hover {
            background: #545b62;
        }
        
        .info-section {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .info-section h3 {
            color: #0066cc;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .info-section p {
            color: #004499;
            line-height: 1.6;
            font-size: 1.1rem;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .demos-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .demo-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🚀 LiteOFD 开发演示</h1>
            <p>OFD 文档解析和渲染库 - 演示导航页面</p>
        </div>
        
        <div class="content">
            <!-- 重要信息 -->
            <div class="info-section">
                <h3>💡 开发服务器已启动</h3>
                <p>
                    你现在可以通过以下链接访问不同的演示页面。
                    所有演示都运行在开发服务器上，支持热重载和实时预览。
                </p>
            </div>
            
            <!-- 原生演示 -->
            <div class="section">
                <h2>📄 原生 JavaScript 演示</h2>
                <div class="demos-grid">
                    <div class="demo-card featured">
                        <div class="demo-header">
                            <div class="demo-icon">🔍</div>
                            <div class="demo-title">原生 OFD 查看器</div>
                        </div>
                        <div class="demo-description">
                            基于原生 JavaScript 的 OFD 文档查看器，展示 LiteOFD 核心库的完整功能。
                            包含文件上传、页面导航、缩放控制、搜索等功能。
                        </div>
                        <a href="/index.html" class="demo-button">查看原生演示</a>
                    </div>
                </div>
            </div>
            
            <!-- Vue 演示 -->
            <div class="section">
                <h2>🔧 Vue 组件演示</h2>
                <div class="demos-grid">
                    <div class="demo-card featured">
                        <div class="demo-header">
                            <div class="demo-icon">🎯</div>
                            <div class="demo-title">Vue 组件演示</div>
                        </div>
                        <div class="demo-description">
                            推荐查看！展示如何在 Vue 项目中集成和使用封装好的 OfdViewer 组件。
                            包含现代化的用户界面和完整的功能控制。
                        </div>
                        <a href="/example/vue-demo/vue-demo.html" class="demo-button">查看 Vue 组件演示</a>
                    </div>
                    
                    <div class="demo-card">
                        <div class="demo-header">
                            <div class="demo-icon">📦</div>
                            <div class="demo-title">Vue SFC 示例</div>
                        </div>
                        <div class="demo-description">
                            单文件组件格式的完整集成示例，展示如何在实际 Vue 项目中使用 OfdViewer 组件。
                        </div>
                        <a href="/example/vue-demo/demo.vue" class="demo-button secondary">查看 SFC 示例</a>
                    </div>
                </div>
            </div>
            
            <!-- 开发信息 -->
            <div class="info-section">
                <h3>🛠️ 开发信息</h3>
                <p>
                    开发服务器运行在 <strong>http://localhost:3000</strong><br>
                    Vue 组件源码位于：<code>src/ofdViewer/ofdViewer.vue</code><br>
                    核心库源码位于：<code>src/liteofd/</code>
                </p>
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="footer">
            <p>🔗 LiteOFD - 轻量级 OFD 文档解析和渲染库</p>
        </div>
    </div>
</body>
</html>
