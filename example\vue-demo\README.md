# OFD Viewer Vue 组件演示

这个目录包含了展示如何在 Vue 项目中使用封装好的 `OfdViewer` 组件的演示文件。

## 📁 文件说明

### 1. `vue-demo.html` + `vue-demo.js` - Vue 组件演示 ⭐ 推荐
- 完整的 Vue 组件集成演示
- 现代化的用户界面设计
- 包含侧边栏控制面板和实时状态监控
- 展示如何使用封装好的 `OfdViewer` 组件

### 2. `demo.vue` - Vue SFC 示例
- 单文件组件格式的完整集成示例
- TypeScript 支持
- 可直接在 Vue 项目中使用的模板
- 包含详细的使用说明和代码示例

### 3. `main.ts` - TypeScript 入口文件
- 项目的 TypeScript 入口文件
- 用于开发服务器的模块加载

## 🚀 如何运行演示

### 方法一：通过开发服务器访问 ⭐ 推荐

1. 在项目根目录运行开发服务器：
```bash
npm run dev
```

2. 开发服务器启动后，访问以下地址：
- **开发首页**：http://localhost:3000/dev-index.html
- **Vue 组件演示**：http://localhost:3000/example/vue-demo/vue-demo.html
- **Vue SFC 示例**：http://localhost:3000/example/vue-demo/demo.vue

### 方法二：直接在浏览器中打开

由于演示使用了 CDN 引入 Vue 3，可以直接在浏览器中打开：
- `vue-demo.html` - 完整的 Vue 组件演示

## 🔧 项目配置说明

### Vite 配置更新

项目的 `vite.config.ts` 已经配置了 Vue 支持：

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [
    vue(), // 添加 Vue 插件支持
    // ... 其他插件
  ],
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true, // 自动打开浏览器
    cors: true, // 允许跨域
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'), // 设置 @ 指向 src 目录
    },
  },
})
```

### 开发首页

项目根目录的 `dev-index.html` 提供了所有演示的导航链接，包括：
- 原生 JavaScript 演示
- Vue 组件演示
- Vue SFC 示例

## 💻 在实际项目中使用

### 1. 导入封装好的 OfdViewer 组件

```vue
<script setup lang="ts">
// 导入封装好的组件
import OfdViewer from '@/src/ofdViewer/ofdViewer.vue'
</script>
```

### 2. 使用组件

```vue
<template>
  <OfdViewer
    ref="ofdViewerRef"
    :src="ofdFile"
    :show-toolbar="true"
    :auto-load="true"
    @loaded="onDocumentLoaded"
    @error="onDocumentError"
    @page-change="onPageChange"
    @scale-change="onScaleChange"
    @signature-click="onSignatureClick"
  />
</template>
```

### 3. 处理事件

```typescript
const onDocumentLoaded = (document: OfdDocument) => {
  console.log('文档加载成功:', document)
}

const onDocumentError = (error: Error) => {
  console.error('文档加载失败:', error)
}

const onPageChange = (page: number) => {
  console.log('页面变化:', page)
}

const onScaleChange = (scale: number) => {
  console.log('缩放变化:', scale)
}

const onSignatureClick = (data: any) => {
  console.log('签名点击:', data)
}
```

## 📋 组件 API

### Props
- `src`: OFD 文件源（String | File | ArrayBuffer）
- `showToolbar`: 是否显示工具栏（Boolean，默认 true）
- `autoLoad`: 是否自动加载（Boolean，默认 true）
- `scale`: 初始缩放比例（Number，默认 1）
- `pageWrapStyle`: 页面包装样式（String）
- `pageIndexes`: 指定渲染的页面（Number[]）

### Events
- `loaded`: 文档加载完成
- `error`: 加载错误
- `pageChange`: 页面变化
- `scaleChange`: 缩放变化
- `signatureClick`: 签名点击

### 暴露的方法
- `loadFile()`: 加载文件
- `goToPage()`: 跳转到指定页面
- `firstPage()`, `prevPage()`, `nextPage()`, `lastPage()`: 页面导航
- `zoomIn()`, `zoomOut()`, `resetZoom()`, `setZoom()`: 缩放控制
- `search()`: 搜索内容
- `getContent()`: 获取内容
- `getCurrentPage()`, `getTotalPages()`, `getScale()`: 获取状态

## 🛠️ 开发信息

- **Vue 版本**: 3.x
- **TypeScript**: 支持
- **组件位置**: `src/ofdViewer/ofdViewer.vue`
- **核心库**: `src/liteofd/`
- **开发服务器**: http://localhost:3000

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 查看浏览器控制台的错误信息
2. 检查组件的 props 和事件绑定
3. 参考演示文件中的用法
4. 查看 `src/ofdViewer/ofdViewer.vue` 源码

## 📄 许可证

本项目遵循 MIT 许可证。
