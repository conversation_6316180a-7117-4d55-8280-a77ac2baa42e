<template>
  <div class="ofd-demo-container">
    <!-- 页面头部 -->
    <div class="demo-header">
      <h1>🔍 OFD Viewer Vue SFC 示例</h1>
      <p>展示如何在 Vue 单文件组件中使用 OfdViewer</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-group">
        <label>选择 OFD 文件:</label>
        <input
          type="file"
          accept=".ofd"
          @change="handleFileUpload"
          class="file-input"
        />
      </div>

      <div class="control-group">
        <button @click="loadSampleFile" class="btn btn-primary">
          加载示例文件
        </button>
      </div>
    </div>

    <!-- 状态信息 -->
    <div v-if="documentInfo.fileName" class="status-info">
      <div class="status-item">
        <span class="label">文件名:</span>
        <span class="value">{{ documentInfo.fileName }}</span>
      </div>
      <div class="status-item">
        <span class="label">页数:</span>
        <span class="value">{{ documentInfo.totalPages }}</span>
      </div>
      <div class="status-item">
        <span class="label">当前页:</span>
        <span class="value">{{ documentInfo.currentPage }}</span>
      </div>
      <div class="status-item">
        <span class="label">缩放:</span>
        <span class="value">{{ Math.round(documentInfo.scale * 100) }}%</span>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- OFD 查看器组件 -->
    <div class="viewer-container">
      <!--
        在实际项目中，这里应该使用真实的 OfdViewer 组件：
        <OfdViewer
          ref="ofdViewerRef"
          :src="currentFile"
          :show-toolbar="true"
          :auto-load="true"
          @loaded="onDocumentLoaded"
          @error="onDocumentError"
          @page-change="onPageChange"
          @scale-change="onScaleChange"
          @signature-click="onSignatureClick"
        />
      -->

      <!-- 这里是演示用的占位符 -->
      <div class="viewer-placeholder">
        <div v-if="!currentFile" class="placeholder-content">
          <h3>📄 OFD Viewer 组件</h3>
          <p>请选择 OFD 文件进行预览</p>
          <div class="feature-list">
            <h4>组件特性：</h4>
            <ul>
              <li>✅ 完整的 OFD 文档解析和渲染</li>
              <li>✅ 内置工具栏和导航控制</li>
              <li>✅ 响应式设计，适配各种屏幕</li>
              <li>✅ 丰富的事件回调机制</li>
              <li>✅ TypeScript 类型支持</li>
              <li>✅ 数字签名识别和交互</li>
            </ul>
          </div>
        </div>

        <div v-else class="document-preview">
          <h3>📄 {{ documentInfo.fileName }}</h3>
          <p>在实际使用中，这里会显示真实的 OFD 文档内容</p>
          <div class="mock-document">
            <div class="mock-page">
              <div class="mock-content">
                <div class="mock-line"></div>
                <div class="mock-line short"></div>
                <div class="mock-line"></div>
                <div class="mock-line medium"></div>
                <div class="mock-line"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="usage-info">
      <h3>💡 在实际项目中使用</h3>
      <div class="code-example">
        <h4>1. 导入组件</h4>
        <pre><code>import OfdViewer from '@/src/ofdViewer/ofdViewer.vue'</code></pre>

        <h4>2. 注册组件</h4>
        <pre><code>export default {
  components: {
    OfdViewer
  }
}</code></pre>

        <h4>3. 使用组件</h4>
        <pre><code>&lt;OfdViewer
  :src="ofdFile"
  :show-toolbar="true"
  @loaded="onDocumentLoaded"
  @error="onDocumentError"
/&gt;</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
// 在实际项目中，应该这样导入 OfdViewer 组件：
// import OfdViewer from '@/src/ofdViewer/ofdViewer.vue'

// 响应式数据
const ofdViewerRef = ref(null)
const currentFile = ref<File | null>(null)
const errorMessage = ref('')

const documentInfo = reactive({
  fileName: '',
  totalPages: 0,
  currentPage: 1,
  scale: 1
})

// 文件处理方法
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    if (!file.name.toLowerCase().endsWith('.ofd')) {
      errorMessage.value = '请选择 .ofd 格式的文件'
      return
    }

    errorMessage.value = ''
    currentFile.value = file
    documentInfo.fileName = file.name
    documentInfo.totalPages = Math.floor(Math.random() * 10) + 3 // 模拟页数
    documentInfo.currentPage = 1
    documentInfo.scale = 1

    console.log(`选择文件: ${file.name}`)
  }
}

const loadSampleFile = () => {
  errorMessage.value = '请上传一个真实的 OFD 文件进行演示'
  console.log('加载示例文件功能需要提供实际的 OFD 文件')
}

// OFD Viewer 事件处理（在实际使用中会用到）
const onDocumentLoaded = (document: any) => {
  console.log('文档加载成功:', document)
  // 更新文档信息
  if (ofdViewerRef.value) {
    // documentInfo.totalPages = ofdViewerRef.value.getTotalPages()
    // documentInfo.currentPage = ofdViewerRef.value.getCurrentPage()
    // documentInfo.scale = ofdViewerRef.value.getScale()
  }
}

const onDocumentError = (error: Error) => {
  console.error('文档加载失败:', error)
  errorMessage.value = `文档加载失败: ${error.message}`
}

const onPageChange = (page: number) => {
  documentInfo.currentPage = page
  console.log(`页面变化: ${page}`)
}

const onScaleChange = (scale: number) => {
  documentInfo.scale = scale
  console.log(`缩放变化: ${Math.round(scale * 100)}%`)
}

const onSignatureClick = (data: any) => {
  console.log('签名点击:', data)
}
</script>

<style scoped>
.ofd-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border-radius: 12px;
}

.demo-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.demo-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.control-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  font-weight: 500;
  color: #495057;
}

.file-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-primary {
  background: #4a90e2;
  color: white;
}

.btn-primary:hover {
  background: #357abd;
  transform: translateY(-1px);
}

.status-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 20px;
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item .label {
  font-weight: 500;
  color: #0066cc;
}

.status-item .value {
  color: #004499;
  font-weight: 600;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.viewer-container {
  height: 600px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}

.viewer-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.placeholder-content {
  text-align: center;
  padding: 40px;
  max-width: 500px;
}

.placeholder-content h3 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 1.8rem;
}

.placeholder-content p {
  color: #6c757d;
  margin-bottom: 25px;
  font-size: 1.1rem;
}

.feature-list {
  text-align: left;
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.feature-list h4 {
  color: #495057;
  margin-bottom: 15px;
}

.feature-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 8px 0;
  color: #6c757d;
  font-size: 14px;
}

.document-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.document-preview h3 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.document-preview p {
  color: #6c757d;
  margin-bottom: 30px;
}

.mock-document {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  padding: 30px;
  max-width: 400px;
  width: 100%;
}

.mock-page {
  background: #fafafa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 20px;
}

.mock-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mock-line {
  height: 12px;
  background: linear-gradient(90deg, #e9ecef 0%, #f8f9fa 100%);
  border-radius: 6px;
  animation: shimmer 2s infinite;
}

.mock-line.short {
  width: 60%;
}

.mock-line.medium {
  width: 80%;
}

@keyframes shimmer {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.usage-info {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.usage-info h3 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.code-example h4 {
  color: #6c757d;
  margin: 20px 0 10px 0;
  font-size: 1.1rem;
}

.code-example pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  margin-bottom: 15px;
}

.code-example code {
  font-family: 'Courier New', monospace;
  color: #495057;
  font-size: 14px;
}

@media (max-width: 768px) {
  .ofd-demo-container {
    padding: 10px;
  }

  .demo-header {
    padding: 20px;
  }

  .demo-header h1 {
    font-size: 2rem;
  }

  .control-panel {
    flex-direction: column;
    gap: 15px;
  }

  .status-info {
    grid-template-columns: 1fr;
  }

  .viewer-container {
    height: 400px;
  }
}
</style>