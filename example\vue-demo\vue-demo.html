<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OFD Viewer Vue 组件演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 700px;
        }
        
        .sidebar {
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .control-section {
            margin-bottom: 30px;
        }
        
        .control-section h4 {
            color: #6c757d;
            margin-bottom: 12px;
            font-size: 1rem;
        }
        
        .file-upload {
            width: 100%;
            padding: 12px;
            border: 2px dashed #ced4da;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .file-upload:hover {
            border-color: #4a90e2;
            background: #f0f7ff;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            background: #4a90e2;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #357abd;
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            background: #ced4da;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .status-panel {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .status-label {
            color: #6c757d;
        }
        
        .status-value {
            color: #495057;
            font-weight: 500;
        }
        
        .viewer-area {
            padding: 30px;
            background: #fff;
        }
        
        .viewer-container {
            height: 640px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
        }
        
        .demo-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .demo-info h4 {
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .demo-info p {
            color: #004499;
            line-height: 1.6;
        }
        
        .demo-info code {
            background: #fff;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #d63384;
        }
        
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                border-right: none;
                border-bottom: 1px solid #dee2e6;
            }
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .sidebar, .viewer-area {
                padding: 20px;
            }
            
            .viewer-container {
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="header">
                <h1>🔍 OFD Viewer Vue 组件演示</h1>
                <p>展示如何在 Vue 项目中使用封装好的 OfdViewer 组件</p>
            </div>
            
            <div class="main-content">
                <!-- 侧边栏控制面板 -->
                <div class="sidebar">
                    <h3>📋 控制面板</h3>
                    
                    <!-- 文件上传 -->
                    <div class="control-section">
                        <h4>📁 文件选择</h4>
                        <input 
                            type="file" 
                            accept=".ofd" 
                            @change="handleFileUpload"
                            class="file-upload"
                            placeholder="选择 OFD 文件"
                        />
                        <button @click="loadSampleFile" class="btn">
                            📄 加载示例文件
                        </button>
                    </div>
                    
                    <!-- 查看器控制 -->
                    <div class="control-section">
                        <h4>🎛️ 查看器控制</h4>
                        <button @click="zoomIn" :disabled="!isDocumentLoaded" class="btn">
                            🔍 放大
                        </button>
                        <button @click="zoomOut" :disabled="!isDocumentLoaded" class="btn">
                            🔎 缩小
                        </button>
                        <button @click="resetZoom" :disabled="!isDocumentLoaded" class="btn">
                            🎯 重置缩放
                        </button>
                    </div>
                    
                    <!-- 页面导航 -->
                    <div class="control-section">
                        <h4>📖 页面导航</h4>
                        <button @click="goToFirstPage" :disabled="!canGoPrev" class="btn">
                            ⏮️ 首页
                        </button>
                        <button @click="goToPrevPage" :disabled="!canGoPrev" class="btn">
                            ⬅️ 上一页
                        </button>
                        <button @click="goToNextPage" :disabled="!canGoNext" class="btn">
                            ➡️ 下一页
                        </button>
                        <button @click="goToLastPage" :disabled="!canGoNext" class="btn">
                            ⏭️ 末页
                        </button>
                    </div>
                    
                    <!-- 其他功能 -->
                    <div class="control-section">
                        <h4>🔧 其他功能</h4>
                        <button @click="getDocumentContent" :disabled="!isDocumentLoaded" class="btn btn-secondary">
                            📝 获取内容
                        </button>
                    </div>
                    
                    <!-- 状态信息 -->
                    <div class="control-section">
                        <h4>📊 文档状态</h4>
                        <div class="status-panel">
                            <div class="status-item">
                                <span class="status-label">文件名:</span>
                                <span class="status-value">{{ documentStatus.fileName || '未选择' }}</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">总页数:</span>
                                <span class="status-value">{{ documentStatus.totalPages || 0 }}</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">当前页:</span>
                                <span class="status-value">{{ documentStatus.currentPage || 0 }}</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">缩放比例:</span>
                                <span class="status-value">{{ Math.round((documentStatus.scale || 1) * 100) }}%</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">加载状态:</span>
                                <span class="status-value">{{ documentStatus.loadingStatus }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 查看器区域 -->
                <div class="viewer-area">
                    <!-- 使用说明 -->
                    <div class="demo-info">
                        <h4>💡 使用说明</h4>
                        <p>
                            这个演示展示了如何在 Vue 项目中集成 <code>OfdViewer</code> 组件。
                            在实际项目中，你需要导入位于 <code>src/ofdViewer/ofdViewer.vue</code> 的组件。
                        </p>
                    </div>
                    
                    <!-- OFD 查看器组件 -->
                    <div class="viewer-container">
                        <ofd-viewer
                            ref="ofdViewerRef"
                            :src="currentFile"
                            :show-toolbar="viewerConfig.showToolbar"
                            :auto-load="viewerConfig.autoLoad"
                            :scale="viewerConfig.scale"
                            @loaded="onDocumentLoaded"
                            @error="onDocumentError"
                            @page-change="onPageChange"
                            @scale-change="onScaleChange"
                            @signature-click="onSignatureClick"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- 主应用脚本 -->
    <script src="./vue-demo.js"></script>
</body>
</html>
