// Vue 3 应用配置 - OFD Viewer 组件演示
const { createApp, ref, reactive, computed, onMounted } = Vue

// 模拟的 OfdViewer 组件（在实际项目中应该从 src/ofdViewer/ofdViewer.vue 导入）
const OfdViewer = {
  template: `
    <div class="ofd-viewer-demo">
      <div v-if="showToolbar" class="ofd-toolbar">
        <div class="toolbar-section">
          <input 
            ref="fileInput" 
            type="file" 
            accept=".ofd" 
            @change="handleFileChange" 
            style="display: none"
          />
          <button @click="selectFile" class="toolbar-btn primary">
            📁 选择文件
          </button>
          <span v-if="fileName" class="file-info">{{ fileName }}</span>
        </div>
        
        <div class="toolbar-section">
          <button @click="zoomOut" :disabled="!isLoaded" class="toolbar-btn">🔎 缩小</button>
          <button @click="zoomIn" :disabled="!isLoaded" class="toolbar-btn">🔍 放大</button>
          <button @click="resetZoom" :disabled="!isLoaded" class="toolbar-btn">🎯 重置</button>
        </div>
        
        <div class="toolbar-section">
          <button @click="firstPage" :disabled="!canGoPrev" class="toolbar-btn">⏮️ 首页</button>
          <button @click="prevPage" :disabled="!canGoPrev" class="toolbar-btn">⬅️ 上一页</button>
          <span class="page-indicator">{{ currentPage }} / {{ totalPages }}</span>
          <button @click="nextPage" :disabled="!canGoNext" class="toolbar-btn">➡️ 下一页</button>
          <button @click="lastPage" :disabled="!canGoNext" class="toolbar-btn">⏭️ 末页</button>
        </div>
      </div>
      
      <div class="ofd-content" ref="contentContainer">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <h3>🔄 正在加载 OFD 文档...</h3>
          <p>请稍候，正在解析文档内容</p>
        </div>
        
        <div v-else-if="error" class="error-state">
          <div class="error-icon">❌</div>
          <h3>加载失败</h3>
          <p>{{ error }}</p>
          <button @click="retry" class="retry-btn">🔄 重试</button>
        </div>
        
        <div v-else-if="!src" class="welcome-state">
          <div class="welcome-content">
            <div class="welcome-icon">📄</div>
            <h3>欢迎使用 OFD Viewer</h3>
            <p>请选择一个 OFD 文件开始预览</p>
            
            <div class="features-grid">
              <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h4>高质量渲染</h4>
                <p>完美还原 OFD 文档样式</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h4>响应式设计</h4>
                <p>适配各种屏幕尺寸</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h4>高性能</h4>
                <p>快速加载和流畅操作</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <h4>签名支持</h4>
                <p>识别和交互数字签名</p>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="document-state">
          <div class="document-header">
            <h3>📄 {{ fileName }}</h3>
            <div class="document-meta">
              <span class="meta-item">📊 第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
              <span class="meta-item">🔍 缩放 {{ Math.round(currentScale * 100) }}%</span>
            </div>
          </div>
          
          <div class="document-viewport">
            <div class="document-page">
              <div class="page-content">
                <h4>🎯 OFD 文档内容区域</h4>
                <p>在实际使用中，这里会显示真实的 OFD 文档页面</p>
                <div class="content-preview">
                  <div class="preview-line"></div>
                  <div class="preview-line short"></div>
                  <div class="preview-line"></div>
                  <div class="preview-line medium"></div>
                  <div class="preview-line"></div>
                  <div class="preview-line short"></div>
                </div>
                <p class="tech-note">
                  💡 技术说明：真实的 OfdViewer 组件会调用 LiteOfd 引擎来解析和渲染 OFD 文档
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  props: {
    src: [String, File, ArrayBuffer],
    showToolbar: { type: Boolean, default: true },
    autoLoad: { type: Boolean, default: true },
    scale: { type: Number, default: 1 }
  },
  emits: ['loaded', 'error', 'page-change', 'scale-change', 'signature-click'],
  data() {
    return {
      loading: false,
      error: '',
      fileName: '',
      currentPage: 1,
      totalPages: 0,
      currentScale: 1,
      isLoaded: false
    }
  },
  computed: {
    canGoPrev() {
      return this.isLoaded && this.currentPage > 1
    },
    canGoNext() {
      return this.isLoaded && this.currentPage < this.totalPages
    }
  },
  watch: {
    src: {
      handler(newSrc) {
        if (newSrc && this.autoLoad) {
          this.loadFile(newSrc)
        }
      },
      immediate: true
    },
    scale(newScale) {
      if (newScale !== this.currentScale) {
        this.currentScale = newScale
      }
    }
  },
  methods: {
    loadFile(file) {
      this.loading = true
      this.error = ''
      
      // 模拟加载过程
      setTimeout(() => {
        try {
          if (file instanceof File) {
            this.fileName = file.name
          } else {
            this.fileName = 'document.ofd'
          }
          
          this.isLoaded = true
          this.loading = false
          this.currentPage = 1
          this.totalPages = Math.floor(Math.random() * 15) + 5 // 5-19页
          
          this.$emit('loaded', { 
            pages: this.totalPages,
            fileName: this.fileName 
          })
        } catch (err) {
          this.error = '加载失败: ' + err.message
          this.loading = false
          this.$emit('error', err)
        }
      }, 2000) // 模拟较长的加载时间
    },
    
    retry() {
      if (this.src) {
        this.loadFile(this.src)
      }
    },
    
    selectFile() {
      this.$refs.fileInput?.click()
    },
    
    handleFileChange(event) {
      const file = event.target.files[0]
      if (file) {
        this.loadFile(file)
      }
    },
    
    // 导航方法
    firstPage() {
      this.currentPage = 1
      this.$emit('page-change', this.currentPage)
    },
    
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--
        this.$emit('page-change', this.currentPage)
      }
    },
    
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++
        this.$emit('page-change', this.currentPage)
      }
    },
    
    lastPage() {
      this.currentPage = this.totalPages
      this.$emit('page-change', this.currentPage)
    },
    
    // 缩放方法
    zoomIn() {
      this.currentScale = Math.min(this.currentScale * 1.25, 3)
      this.$emit('scale-change', this.currentScale)
    },
    
    zoomOut() {
      this.currentScale = Math.max(this.currentScale * 0.8, 0.25)
      this.$emit('scale-change', this.currentScale)
    },
    
    resetZoom() {
      this.currentScale = 1
      this.$emit('scale-change', this.currentScale)
    },
    
    // 公开方法
    getCurrentPage() { return this.currentPage },
    getTotalPages() { return this.totalPages },
    getScale() { return this.currentScale },
    getContent() { return `模拟文档内容 - 第${this.currentPage}页` }
  }
}

const App = {
  components: {
    'ofd-viewer': OfdViewer
  },
  
  setup() {
    // 引用
    const ofdViewerRef = ref(null)
    
    // 响应式数据
    const currentFile = ref(null)
    const isDocumentLoaded = ref(false)
    
    const viewerConfig = reactive({
      showToolbar: true,
      autoLoad: true,
      scale: 1
    })
    
    const documentStatus = reactive({
      fileName: '',
      totalPages: 0,
      currentPage: 0,
      scale: 1,
      loadingStatus: '未加载'
    })
    
    // 计算属性
    const canGoPrev = computed(() => {
      return isDocumentLoaded.value && documentStatus.currentPage > 1
    })
    
    const canGoNext = computed(() => {
      return isDocumentLoaded.value && documentStatus.currentPage < documentStatus.totalPages
    })
    
    // 文件处理方法
    const handleFileUpload = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (!file.name.toLowerCase().endsWith('.ofd')) {
          alert('错误：请选择 .ofd 格式的文件')
          return
        }
        
        documentStatus.fileName = file.name
        documentStatus.loadingStatus = '准备加载'
        currentFile.value = file
        
        console.log(`选择文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`)
      }
    }
    
    const loadSampleFile = async () => {
      try {
        documentStatus.loadingStatus = '加载示例文件'
        console.log('尝试加载示例文件...')
        
        // 模拟示例文件加载
        setTimeout(() => {
          alert('示例文件功能需要提供实际的 OFD 文件')
          documentStatus.loadingStatus = '示例文件不可用'
        }, 1000)
        
      } catch (error) {
        console.error(`加载示例文件失败: ${error.message}`)
        documentStatus.loadingStatus = '加载失败'
      }
    }
    
    // OFD Viewer 事件处理
    const onDocumentLoaded = (ofdDocument) => {
      isDocumentLoaded.value = true
      documentStatus.loadingStatus = '加载完成'
      
      if (ofdViewerRef.value) {
        documentStatus.totalPages = ofdViewerRef.value.getTotalPages()
        documentStatus.currentPage = ofdViewerRef.value.getCurrentPage()
        documentStatus.scale = ofdViewerRef.value.getScale()
      }
      
      console.log(`文档加载成功: ${documentStatus.totalPages} 页`)
    }
    
    const onDocumentError = (error) => {
      isDocumentLoaded.value = false
      documentStatus.loadingStatus = '加载失败'
      console.error(`文档加载失败: ${error.message}`)
    }
    
    const onPageChange = (page) => {
      documentStatus.currentPage = page
      console.log(`页面切换到: ${page}`)
    }
    
    const onScaleChange = (scale) => {
      documentStatus.scale = scale
      viewerConfig.scale = scale
      console.log(`缩放变更为: ${Math.round(scale * 100)}%`)
    }
    
    const onSignatureClick = (data) => {
      console.log('检测到签名点击事件', data)
      alert('🔐 检测到数字签名点击事件，详情请查看控制台')
    }
    
    // 控制方法
    const zoomIn = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.zoomIn()
        console.log('执行放大操作')
      }
    }
    
    const zoomOut = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.zoomOut()
        console.log('执行缩小操作')
      }
    }
    
    const resetZoom = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.resetZoom()
        console.log('重置缩放比例')
      }
    }
    
    const goToFirstPage = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.firstPage()
        console.log('跳转到首页')
      }
    }
    
    const goToPrevPage = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.prevPage()
        console.log('跳转到上一页')
      }
    }
    
    const goToNextPage = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.nextPage()
        console.log('跳转到下一页')
      }
    }
    
    const goToLastPage = () => {
      if (ofdViewerRef.value) {
        ofdViewerRef.value.lastPage()
        console.log('跳转到末页')
      }
    }
    
    const getDocumentContent = () => {
      if (ofdViewerRef.value) {
        const content = ofdViewerRef.value.getContent()
        console.log(`获取文档内容: ${content.length} 字符`, content)
        alert(`📝 文档内容已输出到控制台\n内容长度: ${content.length} 字符`)
      }
    }
    
    // 生命周期
    onMounted(() => {
      console.log('🚀 OFD Viewer Vue 演示应用已启动')
    })
    
    return {
      // 引用
      ofdViewerRef,
      
      // 数据
      currentFile,
      isDocumentLoaded,
      viewerConfig,
      documentStatus,
      
      // 计算属性
      canGoPrev,
      canGoNext,
      
      // 方法
      handleFileUpload,
      loadSampleFile,
      onDocumentLoaded,
      onDocumentError,
      onPageChange,
      onScaleChange,
      onSignatureClick,
      zoomIn,
      zoomOut,
      resetZoom,
      goToFirstPage,
      goToPrevPage,
      goToNextPage,
      goToLastPage,
      getDocumentContent
    }
  }
}

// 创建并挂载应用
createApp(App).mount('#app')
